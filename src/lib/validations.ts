import { z } from 'zod';

// Base validation schemas
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address');

export const phoneSchema = z
  .string()
  .optional()
  .refine((val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val), {
    message: 'Please enter a valid phone number',
  });

export const nameSchema = z
  .string()
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s\-']+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes');

export const companySchema = z
  .string()
  .min(2, 'Company name must be at least 2 characters')
  .max(100, 'Company name must be less than 100 characters');

// Contact form schema
export const contactFormSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  company: companySchema,
  jobTitle: z.string().max(100, 'Job title must be less than 100 characters').optional(),
  message: z.string().max(1000, 'Message must be less than 1000 characters').optional(),
  preferredContactMethod: z.enum(['email', 'phone'], {
    required_error: 'Please select a preferred contact method',
  }),
  consent: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the privacy policy to continue',
  }),
});

// Demo request form schema
export const demoRequestFormSchema = contactFormSchema.extend({
  facilityType: z.enum(['hospital', 'nursing-home', 'clinic', 'assisted-living', 'other'], {
    required_error: 'Please select your facility type',
  }),
  facilitySize: z.enum(['small', 'medium', 'large', 'enterprise'], {
    required_error: 'Please select your facility size',
  }),
  currentSystem: z.string().max(200, 'Current system description must be less than 200 characters').optional(),
  timeline: z.enum(['immediate', '1-3-months', '3-6-months', '6-12-months', 'planning'], {
    required_error: 'Please select your implementation timeline',
  }),
  specificRequirements: z.string().max(1000, 'Requirements must be less than 1000 characters').optional(),
});

// Consultation form schema
export const consultationFormSchema = contactFormSchema.extend({
  consultationType: z.enum(['technical', 'commercial', 'implementation', 'support'], {
    required_error: 'Please select consultation type',
  }),
  urgency: z.enum(['low', 'medium', 'high', 'urgent'], {
    required_error: 'Please select urgency level',
  }),
  preferredDate: z.string().optional(),
  preferredTime: z.string().optional(),
  topics: z.array(z.string()).min(1, 'Please select at least one topic'),
});

// Configurator form schema
export const configuratorFormSchema = z.object({
  facilityType: z.enum(['hospital', 'nursing-home', 'clinic', 'assisted-living', 'other'], {
    required_error: 'Please select your facility type',
  }),
  numberOfBeds: z.number().min(1, 'Number of beds must be at least 1').max(10000, 'Number of beds seems too high'),
  numberOfStations: z.number().min(1, 'Number of stations must be at least 1').max(1000, 'Number of stations seems too high'),
  coverage: z.enum(['basic', 'standard', 'premium', 'enterprise'], {
    required_error: 'Please select coverage level',
  }),
  features: z.array(z.string()).min(1, 'Please select at least one feature'),
  budget: z.enum(['under-50k', '50k-100k', '100k-250k', '250k-500k', 'over-500k']).optional(),
  contactInfo: contactFormSchema,
});

// File upload validation
export const fileUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine((file) => file.size <= 5 * 1024 * 1024, 'File size must be less than 5MB')
    .refine(
      (file) => ['image/jpeg', 'image/png', 'image/webp', 'image/gif'].includes(file.type),
      'Only JPEG, PNG, WebP, and GIF files are allowed'
    ),
});

// Utility functions for validation
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
}

export function validateEmail(email: string): boolean {
  try {
    emailSchema.parse(email);
    return true;
  } catch {
    return false;
  }
}

export function validatePhone(phone: string): boolean {
  try {
    phoneSchema.parse(phone);
    return true;
  } catch {
    return false;
  }
}

// Form error formatting
export function formatValidationErrors(error: z.ZodError): Record<string, string> {
  const errors: Record<string, string> = {};
  
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    errors[path] = err.message;
  });
  
  return errors;
}

// Rate limiting helpers
export function createRateLimiter(maxAttempts: number, windowMs: number) {
  const attempts = new Map<string, { count: number; resetTime: number }>();
  
  return {
    isAllowed: (identifier: string): boolean => {
      const now = Date.now();
      const userAttempts = attempts.get(identifier);
      
      if (!userAttempts || now > userAttempts.resetTime) {
        attempts.set(identifier, { count: 1, resetTime: now + windowMs });
        return true;
      }
      
      if (userAttempts.count >= maxAttempts) {
        return false;
      }
      
      userAttempts.count++;
      return true;
    },
    getRemainingTime: (identifier: string): number => {
      const userAttempts = attempts.get(identifier);
      if (!userAttempts) return 0;
      
      return Math.max(0, userAttempts.resetTime - Date.now());
    },
  };
}
