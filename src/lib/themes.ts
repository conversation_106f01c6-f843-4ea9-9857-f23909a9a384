export type ThemeMode = 'light' | 'dark';

export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    accent: string;
    accentForeground: string;
    muted: string;
    mutedForeground: string;
    background: string;
    foreground: string;
    card: string;
    cardForeground: string;
    border: string;
    input: string;
    ring: string;
  };
}

export const themes: Record<ThemeMode, ThemeConfig> = {
  light: {
    name: 'AITELMED Light',
    colors: {
      primary: 'hsl(217 91% 60%)', // AITELMED blue - professional healthcare blue
      primaryForeground: 'hsl(0 0% 100%)',
      secondary: 'hsl(210 40% 96%)',
      secondaryForeground: 'hsl(222.2 84% 4.9%)',
      accent: 'hsl(142 76% 36%)', // Trust-building green for certifications
      accentForeground: 'hsl(0 0% 100%)',
      muted: 'hsl(210 40% 96%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(222.2 84% 4.9%)',
      border: 'hsl(214.3 31.8% 91.4%)',
      input: 'hsl(214.3 31.8% 91.4%)',
      ring: 'hsl(217 91% 60%)',
    },
  },
  dark: {
    name: 'AITELMED Dark',
    colors: {
      primary: 'hsl(217 91% 70%)', // AITELMED blue - slightly lighter for dark mode contrast
      primaryForeground: 'hsl(222.2 84% 4.9%)',
      secondary: 'hsl(217 32% 17%)',
      secondaryForeground: 'hsl(210 40% 98%)',
      accent: 'hsl(142 76% 45%)', // Trust-building green - adjusted for dark mode
      accentForeground: 'hsl(222.2 84% 4.9%)',
      muted: 'hsl(217 32% 17%)',
      mutedForeground: 'hsl(215 20% 65%)',
      background: 'hsl(222.2 84% 4.9%)',
      foreground: 'hsl(210 40% 98%)',
      card: 'hsl(222.2 84% 4.9%)',
      cardForeground: 'hsl(210 40% 98%)',
      border: 'hsl(217 32% 17%)',
      input: 'hsl(217 32% 17%)',
      ring: 'hsl(217 91% 70%)',
    },
  },
};

export function applyTheme(mode: ThemeMode) {
  const themeConfig = themes[mode];
  const root = document.documentElement;

  // Apply CSS custom properties
  Object.entries(themeConfig.colors).forEach(([key, value]) => {
    const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    root.style.setProperty(cssVar, value);
  });

  // Toggle dark class for compatibility with existing CSS
  if (mode === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
}

export function getModeFromUrl(): ThemeMode {
  if (typeof window === 'undefined') return 'light';

  const params = new URLSearchParams(window.location.search);
  const mode = params.get('mode') as ThemeMode;

  return mode && mode in themes ? mode : 'light';
}

export function getSystemPreference(): ThemeMode {
  if (typeof window === 'undefined') return 'light';

  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

export function getStoredMode(): ThemeMode | null {
  if (typeof window === 'undefined') return null;

  try {
    const stored = localStorage.getItem('theme-mode');
    return stored && (stored === 'light' || stored === 'dark') ? stored : null;
  } catch {
    return null;
  }
}

export function setStoredMode(mode: ThemeMode): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('theme-mode', mode);
  } catch {
    // Silently fail if localStorage is not available
  }
}

export function getInitialMode(): ThemeMode {
  // Priority: URL parameter > localStorage > system preference > default (light)
  const urlMode = getModeFromUrl();
  if (urlMode !== 'light') return urlMode;

  const storedMode = getStoredMode();
  if (storedMode) return storedMode;

  return getSystemPreference();
}
