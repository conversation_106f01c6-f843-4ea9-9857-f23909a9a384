'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/components/theme-provider';
import { Sun, Moon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'default' | 'lg' | 'icon';
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  showLabel?: boolean;
}

export function ThemeToggle({ 
  className, 
  size = 'icon', 
  variant = 'ghost',
  showLabel = false 
}: ThemeToggleProps) {
  const { mode, toggleMode, mounted } = useTheme();

  // Show loading state while mounting to prevent hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn('transition-all duration-200', className)}
        disabled
        aria-label="Loading theme toggle"
      >
        <div className="h-4 w-4 animate-pulse rounded-full bg-muted" />
        {showLabel && <span className="ml-2">Loading...</span>}
      </Button>
    );
  }

  const isDark = mode === 'dark';
  const Icon = isDark ? Sun : Moon;
  const label = isDark ? 'Switch to light mode' : 'Switch to dark mode';
  const displayText = isDark ? 'Light' : 'Dark';

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleMode}
      className={cn(
        'transition-all duration-200 hover:scale-105 active:scale-95',
        'focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
        className
      )}
      aria-label={label}
      title={label}
    >
      <Icon 
        className={cn(
          'transition-all duration-300',
          size === 'sm' ? 'h-3 w-3' : 'h-4 w-4',
          isDark ? 'rotate-0 scale-100' : 'rotate-90 scale-0',
          'absolute'
        )} 
      />
      <Moon 
        className={cn(
          'transition-all duration-300',
          size === 'sm' ? 'h-3 w-3' : 'h-4 w-4',
          !isDark ? 'rotate-0 scale-100' : 'rotate-90 scale-0',
          'absolute'
        )} 
      />
      {showLabel && (
        <span className="ml-2 font-medium">
          {displayText}
        </span>
      )}
    </Button>
  );
}

// Compact version for mobile/small spaces
export function ThemeToggleCompact({ className }: { className?: string }) {
  return (
    <ThemeToggle 
      className={className}
      size="sm"
      variant="ghost"
      showLabel={false}
    />
  );
}

// Version with text label for desktop
export function ThemeToggleWithLabel({ className }: { className?: string }) {
  return (
    <ThemeToggle 
      className={className}
      size="default"
      variant="ghost"
      showLabel={true}
    />
  );
}

// Outline version for emphasis
export function ThemeToggleOutline({ className }: { className?: string }) {
  return (
    <ThemeToggle 
      className={className}
      size="icon"
      variant="outline"
      showLabel={false}
    />
  );
}
