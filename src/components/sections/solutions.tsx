'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Stethoscope, 
  Heart, 
  Brain, 
  ArrowRight, 
  CheckCircle,
  Zap,
  Shield,
  Smartphone
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SolutionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  badge?: string;
  href: string;
  className?: string;
}

function SolutionCard({ icon, title, description, features, badge, href, className }: SolutionCardProps) {
  return (
    <Card className={cn(
      "group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-border/50 hover:border-primary/20",
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors">
              {icon}
            </div>
            <div>
              <CardTitle className="text-xl font-semibold group-hover:text-primary transition-colors">
                {title}
              </CardTitle>
              {badge && (
                <Badge variant="secondary" className="mt-1 text-xs">
                  {badge}
                </Badge>
              )}
            </div>
          </div>
        </div>
        <CardDescription className="text-base text-muted-foreground mt-3">
          {description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center space-x-2 text-sm">
              <CheckCircle className="w-4 h-4 text-accent flex-shrink-0" />
              <span className="text-muted-foreground">{feature}</span>
            </li>
          ))}
        </ul>
        
        <Button variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors" asChild>
          <Link href={href}>
            Learn More
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>
        </Button>
      </CardContent>
      
      {/* Hover Effect Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
    </Card>
  );
}

export function SolutionsSection() {
  const solutions = [
    {
      icon: <Stethoscope className="w-6 h-6 text-primary" />,
      title: "medPower® Telemonitoring",
      description: "Zertifizierte Telemonitoring-Plattform für die digitale Betreuung chronisch erkrankter Patienten mit Echtzeit-Überwachung.",
      features: [
        "Vitaldatenübertragung (Blutdruck, Gewicht, EKG)",
        "Echtzeit-Warnsystem und Alarme",
        "Automatisierte Prozesse",
        "GBA-konforme Datenhaltung"
      ],
      badge: "Beliebteste Lösung",
      href: "#medpower"
    },
    {
      icon: <Heart className="w-6 h-6 text-primary" />,
      title: "Patientenfragebögen (PRM)",
      description: "Digitale Patientenfragebögen zur systematischen Erfassung von Gesundheitsdaten und Symptomen.",
      features: [
        "Individualisierte Fragebögen",
        "Automatische Auswertung",
        "Trend-Analyse",
        "Integration in Behandlungsplan"
      ],
      href: "#patient-surveys"
    },
    {
      icon: <Brain className="w-6 h-6 text-primary" />,
      title: "Medikationsverwaltung",
      description: "Intelligente Medikationsverwaltung mit Erinnerungen und Compliance-Überwachung für bessere Therapietreue.",
      features: [
        "Medikationsplan-Verwaltung",
        "Einnahme-Erinnerungen",
        "Compliance-Monitoring",
        "Nebenwirkungen-Tracking"
      ],
      badge: "KI-gestützt",
      href: "#medication"
    }
  ];

  return (
    <section id="solutions" className="py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <Badge variant="secondary" className="mb-4">
            Unsere Services
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Komplette Telemedizin-Lösungen für die digitale Patientenversorgung
          </h2>
          <p className="text-lg text-muted-foreground">
            Von der Vitaldatenübertragung bis zur intelligenten Medikationsverwaltung -
            unsere integrierten Lösungen transformieren die Patientenbetreuung und verbessern die Behandlungsergebnisse.
          </p>
        </div>

        {/* Solutions Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {solutions.map((solution, index) => (
            <SolutionCard
              key={solution.title}
              {...solution}
              className={index === 0 ? "md:col-span-2 lg:col-span-1" : ""}
            />
          ))}
        </div>

        {/* Key Benefits */}
        <div className="bg-card rounded-2xl p-8 border border-border">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">GBA-konform</h3>
              <p className="text-sm text-muted-foreground">
                Zertifizierte Telemedizin-Plattform nach deutschen Gesundheitsstandards und DSGVO-konform
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">Cloud-basierte Technologie</h3>
              <p className="text-sm text-muted-foreground">
                Moderne cloudbasierte Architektur für nahtlose Integration und Skalierbarkeit
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">Web- & mobilfähig</h3>
              <p className="text-sm text-muted-foreground">
                Responsive Benutzeroberfläche für alle Geräte und Bildschirmgrößen optimiert
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">Patientenzentriert</h3>
              <p className="text-sm text-muted-foreground">
                Entwickelt mit Fokus auf Patientensicherheit und Versorgungsqualität
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-semibold text-foreground mb-4">
            Bereit für die digitale Transformation Ihrer Patientenversorgung?
          </h3>
          <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
            Schließen Sie sich hunderten von Arztpraxen und Gesundheitszentren an, die AITELMED für ihre Telemedizin-Bedürfnisse vertrauen.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/request-demo">
                Kostenlose Demo anfordern
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/configurator">
                Plattform testen
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
