'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Linkedin, 
  Twitter, 
  Youtube,
  ExternalLink
} from 'lucide-react';

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
}

function FooterLink({ href, children, external }: FooterLinkProps) {
  return (
    <Link
      href={href}
      className="text-muted-foreground hover:text-primary transition-colors text-sm flex items-center"
      {...(external && { target: "_blank", rel: "noopener noreferrer" })}
    >
      {children}
      {external && <ExternalLink className="w-3 h-3 ml-1" />}
    </Link>
  );
}

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-muted/30 border-t border-border">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-sm">A</span>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-foreground">AITELMED</h3>
                  <Badge variant="secondary" className="text-xs">
                    GBA-konform | Klasse IIa
                  </Badge>
                </div>
              </div>
              <p className="text-muted-foreground text-sm">
                medPower® ist eine zertifizierte Telemonitoring-Plattform für die digitale
                Patientenversorgung. GBA-konform, DSGVO-konform und als Medizinprodukt
                Klasse IIa zertifiziert.
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <MapPin className="w-4 h-4" />
                  <span>Berlin, Germany</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Phone className="w-4 h-4" />
                  <span>+49 (0) 30 1234 5678</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Services */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">Services</h4>
              <div className="space-y-2">
                <FooterLink href="#medpower">medPower® Platform</FooterLink>
                <FooterLink href="#telemonitoring">Telemonitoring</FooterLink>
                <FooterLink href="#patient-surveys">Patientenfragebögen</FooterLink>
                <FooterLink href="/configurator">Plattform testen</FooterLink>
                <FooterLink href="#integration">Integration</FooterLink>
              </div>
            </div>

            {/* Support & Resources */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">Support & Ressourcen</h4>
              <div className="space-y-2">
                <FooterLink href="/request-demo">Demo anfordern</FooterLink>
                <FooterLink href="/schedule-consultation">Beratung vereinbaren</FooterLink>
                <FooterLink href="#documentation" external>Dokumentation</FooterLink>
                <FooterLink href="#support" external>Technischer Support</FooterLink>
                <FooterLink href="#training">Schulungsressourcen</FooterLink>
                <FooterLink href="#partner-portal" external>Partner Portal</FooterLink>
              </div>
            </div>

            {/* Company & Legal */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">Unternehmen</h4>
              <div className="space-y-2">
                <FooterLink href="#about">Über uns</FooterLink>
                <FooterLink href="#careers">Karriere</FooterLink>
                <FooterLink href="#news">News & Presse</FooterLink>
                <FooterLink href="#contact">Kontakt</FooterLink>
                <FooterLink href="#privacy">Datenschutz</FooterLink>
                <FooterLink href="#terms">AGB</FooterLink>
                <FooterLink href="#compliance">Compliance</FooterLink>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Bottom Footer */}
        <div className="py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-sm text-muted-foreground">
              © {currentYear} AITELMED. Alle Rechte vorbehalten. GBA-konforme Telemedizin-Technologie.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">Follow us:</span>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0" asChild>
                  <Link href="#linkedin" target="_blank" rel="noopener noreferrer">
                    <Linkedin className="w-4 h-4" />
                    <span className="sr-only">LinkedIn</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0" asChild>
                  <Link href="#twitter" target="_blank" rel="noopener noreferrer">
                    <Twitter className="w-4 h-4" />
                    <span className="sr-only">Twitter</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0" asChild>
                  <Link href="#youtube" target="_blank" rel="noopener noreferrer">
                    <Youtube className="w-4 h-4" />
                    <span className="sr-only">YouTube</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Language Selector */}
            <div className="flex items-center space-x-2">
              <Globe className="w-4 h-4 text-muted-foreground" />
              <select className="text-sm bg-transparent border-none text-muted-foreground focus:outline-none focus:text-foreground">
                <option value="en">English</option>
                <option value="de">Deutsch</option>
                <option value="fr">Français</option>
                <option value="es">Español</option>
              </select>
            </div>
          </div>
        </div>

        {/* Final CTA Strip */}
        <div className="py-8 border-t border-border">
          <div className="text-center space-y-4">
            <h3 className="text-xl font-semibold text-foreground">
              Bereit für die digitale Transformation Ihrer Patientenversorgung?
            </h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Schließen Sie sich hunderten von Arztpraxen und Gesundheitszentren weltweit an,
              die AITELMED für ihre Telemedizin-Bedürfnisse vertrauen.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/request-demo">
                  Kostenlose Demo anfordern
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/schedule-consultation">
                  Beratung vereinbaren
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
