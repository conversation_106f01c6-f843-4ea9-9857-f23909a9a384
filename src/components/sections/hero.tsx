'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Award, Users, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TrustIndicatorProps {
  icon: React.ReactNode;
  value: string;
  label: string;
}

function TrustIndicator({ icon, value, label }: TrustIndicatorProps) {
  return (
    <div className="flex items-center space-x-3 text-center sm:text-left">
      <div className="flex-shrink-0 w-10 h-10 bg-accent/10 rounded-full flex items-center justify-center">
        {icon}
      </div>
      <div>
        <div className="text-2xl font-bold text-foreground">{value}</div>
        <div className="text-sm text-muted-foreground">{label}</div>
      </div>
    </div>
  );
}

export function HeroSection() {
  const t = useTranslations('hero');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="container mx-auto px-4 py-16 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content Column */}
          <div className="space-y-8">
            {/* Certification Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-accent/10 text-accent border-accent/20">
                <Award className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-4 text-center lg:text-left">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-foreground">
                <span className="text-primary">AITELMED</span>
                <br />
                {t('title')}
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground font-medium">
                <span className="text-primary font-semibold">{t('subtitle')}</span>
              </p>

              <p className="text-lg text-muted-foreground max-w-2xl">
                {t('description')}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button size="lg" className="text-base px-8 py-6 h-auto" asChild>
                <Link href={`/${locale}/request-demo`}>
                  <CheckCircle className="w-5 h-5 mr-2" />
                  {tCta('requestDemo')}
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-base px-8 py-6 h-auto" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  {tCta('scheduleConsultation')}
                </Link>
              </Button>

              {/* <Button variant="secondary" size="lg" className="text-base px-8 py-6 h-auto" asChild>
                <Link href={`/${locale}/configurator`}>
                  {tCta('testPlatform')}
                </Link>
              </Button> */}
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-border">
              <TrustIndicator
                icon={<Award className="w-5 h-5 text-accent" />}
                value={t('trustIndicators.gba')}
                label={t('trustIndicators.gbaLabel')}
              />
              <TrustIndicator
                icon={<Users className="w-5 h-5 text-accent" />}
                value={t('trustIndicators.patients')}
                label={t('trustIndicators.patientsLabel')}
              />
              <TrustIndicator
                icon={<Clock className="w-5 h-5 text-accent" />}
                value={t('trustIndicators.monitoring')}
                label={t('trustIndicators.monitoringLabel')}
              />
            </div>
          </div>

          {/* Visual Column */}
          <div className="relative">
            {/* Hero Image Placeholder */}
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gradient-to-br from-primary/10 to-accent/10 border border-border">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-xl">NC</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-foreground">{t('visual.title')}</h3>
                    <p className="text-muted-foreground">{t('visual.subtitle')}</p>
                  </div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute top-4 right-4">
                <Card className="p-3 shadow-lg">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <div className="w-3 h-3 bg-accent rounded-full animate-pulse" />
                    <span className="text-xs font-medium">{t('visual.statusActive')}</span>
                  </CardContent>
                </Card>
              </div>
              
              <div className="absolute bottom-4 left-4">
                <Card className="p-3 shadow-lg">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-accent" />
                    <span className="text-xs font-medium">{t('visual.certified')}</span>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-accent/10 rounded-full blur-xl" />
          </div>
        </div>

        {/* Additional Trust Indicators */}
        <div className="mt-16 pt-8 border-t border-border">
          <div className="text-center mb-8">
            <p className="text-sm text-muted-foreground uppercase tracking-wide font-medium">
              {t('clientsTitle')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
            {/* Placeholder for client logos */}
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="h-12 bg-muted rounded-lg flex items-center justify-center"
              >
                <span className="text-muted-foreground text-sm font-medium">
                  Praxis Partner {index + 1}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
