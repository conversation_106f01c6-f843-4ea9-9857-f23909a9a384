'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Award, 
  Star, 
  Quote, 
  Shield, 
  CheckCircle,
  Building2,
  Users,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestimonialProps {
  name: string;
  title: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
  facilityType: string;
}

function TestimonialCard({ name, title, company, content, rating, avatar, facilityType }: TestimonialProps) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <div className="flex items-start space-x-4">
          <Avatar className="w-12 h-12">
            <AvatarImage src={avatar} alt={name} />
            <AvatarFallback className="bg-primary/10 text-primary font-semibold">
              {name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-semibold text-foreground">{name}</h4>
              <Badge variant="secondary" className="text-xs">
                {facilityType}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-sm text-muted-foreground font-medium">{company}</p>
            <div className="flex items-center mt-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    "w-4 h-4",
                    i < rating ? "text-yellow-400 fill-current" : "text-muted-foreground"
                  )}
                />
              ))}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <Quote className="w-6 h-6 text-muted-foreground/30 absolute -top-2 -left-1" />
          <p className="text-muted-foreground italic pl-4">
            {content}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

interface CertificationProps {
  name: string;
  description: string;
  icon: React.ReactNode;
  validUntil?: string;
  prominent?: boolean;
}

function CertificationBadge({ name, description, icon, validUntil, prominent }: CertificationProps) {
  return (
    <Card className={cn(
      "text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
      prominent && "ring-2 ring-accent/20 bg-accent/5"
    )}>
      <CardContent className="p-6">
        <div className={cn(
          "w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",
          prominent ? "bg-accent/20" : "bg-primary/10"
        )}>
          {icon}
        </div>
        <h3 className={cn(
          "font-semibold mb-2",
          prominent ? "text-accent" : "text-foreground"
        )}>
          {name}
        </h3>
        <p className="text-sm text-muted-foreground mb-2">{description}</p>
        {validUntil && (
          <p className="text-xs text-muted-foreground">Valid until {validUntil}</p>
        )}
      </CardContent>
    </Card>
  );
}

export function TrustSocialProofSection() {
  const testimonials: TestimonialProps[] = [
    {
      name: "Dr. Maria Schmidt",
      title: "Leitende Ärztin",
      company: "Kardiologische Praxis München",
      content: "Die GBA-Konformität von medPower® gab uns das Vertrauen in die Plattform. Seit der Implementierung haben wir 60% weniger Notfalleinweisungen und unsere Patienten fühlen sich deutlich sicherer betreut.",
      rating: 5,
      facilityType: "Arztpraxis"
    },
    {
      name: "Thomas Weber",
      title: "IT-Leiter",
      company: "Gesundheitszentrum Berlin",
      content: "Die nahtlose Integration in unsere bestehenden Systeme war beeindruckend. Die cloudbasierte Technologie funktioniert einwandfrei und die Schulung des Personals war minimal aufgrund der intuitiven Benutzeroberfläche.",
      rating: 5,
      facilityType: "Gesundheitszentrum"
    },
    {
      name: "Schwester Anna Müller",
      title: "Pflegedienstleitung",
      company: "Chroniker-Betreuung Hamburg",
      content: "Als kleinere Einrichtung brauchten wir eine Lösung, die sowohl leistungsstark als auch kosteneffektiv ist. AITELMED hat genau das geliefert. Das System hat unsere Patientenbetreuung revolutioniert.",
      rating: 5,
      facilityType: "Pflegedienst"
    }
  ];

  const certifications: CertificationProps[] = [
    {
      name: "GBA-konform",
      description: "Zertifizierte Telemedizin-Plattform nach deutschen Gesundheitsstandards",
      icon: <Award className="w-8 h-8 text-accent" />,
      validUntil: "2026",
      prominent: true
    },
    {
      name: "Medizinprodukt Klasse IIa",
      description: "Zertifizierung als Medizinprodukt der Klasse IIa nach MDR",
      icon: <Shield className="w-8 h-8 text-primary" />,
      validUntil: "2025"
    },
    {
      name: "DSGVO-konform",
      description: "Vollständige Konformität mit der Datenschutz-Grundverordnung",
      icon: <CheckCircle className="w-8 h-8 text-primary" />,
      validUntil: "2027"
    },
    {
      name: "ISO 27001",
      description: "Informationssicherheits-Managementsystem Zertifizierung",
      icon: <Shield className="w-8 h-8 text-primary" />,
      validUntil: "2026"
    }
  ];

  const stats = [
    {
      icon: <Building2 className="w-6 h-6 text-primary" />,
      value: "200+",
      label: "Arztpraxen",
      description: "Vertrauen unseren Lösungen"
    },
    {
      icon: <Users className="w-6 h-6 text-primary" />,
      value: "5,000+",
      label: "Fernüberwachte Patienten",
      description: "Nutzen täglich unsere Plattform"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-primary" />,
      value: "99.9%",
      label: "System-Verfügbarkeit",
      description: "Garantierte Zuverlässigkeit"
    },
    {
      icon: <Award className="w-6 h-6 text-primary" />,
      value: "10+",
      label: "Jahre Erfahrung",
      description: "In der Telemedizin"
    }
  ];

  return (
    <section className="py-16 lg:py-24 bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <Badge variant="secondary" className="mb-4">
            Vertrauen & Zuverlässigkeit
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Vertraut von Gesundheitsexperten deutschlandweit
          </h2>
          <p className="text-lg text-muted-foreground">
            Unser Engagement für Qualität, Zuverlässigkeit und Compliance hat das Vertrauen von
            Gesundheitseinrichtungen in ganz Deutschland gewonnen.
          </p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                {stat.icon}
              </div>
              <div className="text-3xl font-bold text-foreground mb-1">{stat.value}</div>
              <div className="font-semibold text-foreground mb-1">{stat.label}</div>
              <div className="text-sm text-muted-foreground">{stat.description}</div>
            </div>
          ))}
        </div>

        {/* Certifications */}
        <div className="mb-16">
          <h3 className="text-2xl font-semibold text-foreground text-center mb-8">
            Zertifizierungen & Compliance
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <CertificationBadge key={index} {...cert} />
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <h3 className="text-2xl font-semibold text-foreground text-center mb-8">
            Was Gesundheitsexperten sagen
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </div>
        </div>

        {/* Client Logos */}
        <div className="text-center">
          <h3 className="text-xl font-semibold text-foreground mb-8">
            Vertraut von führenden Gesundheitseinrichtungen
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="h-16 bg-muted rounded-lg flex items-center justify-center hover:opacity-100 transition-opacity"
              >
                <span className="text-muted-foreground text-sm font-medium">
                  Partner {index + 1}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
