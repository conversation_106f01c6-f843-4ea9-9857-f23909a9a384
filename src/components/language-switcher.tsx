'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Globe } from 'lucide-react';
const locales = ['de', 'pl', 'en'] as const;
type Locale = typeof locales[number];

const languageNames: Record<Locale, string> = {
  de: 'Deutsch',
  pl: 'Polski',
  en: 'English'
};

const languageFlags: Record<Locale, string> = {
  de: '🇩🇪',
  pl: '🇵🇱',
  en: '🇬🇧'
};

interface LanguageSwitcherProps {
  variant?: 'select' | 'buttons';
  className?: string;
}

export function LanguageSwitcher({ variant = 'select', className }: LanguageSwitcherProps) {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale() as Locale;

  const switchLanguage = (newLocale: Locale) => {
    // Remove current locale from pathname
    const pathWithoutLocale = pathname.replace(`/${locale}`, '');
    
    // Get current search params to preserve theme
    const searchParams = new URLSearchParams(window.location.search);
    const searchString = searchParams.toString();
    
    // Construct new URL with new locale
    const newPath = `/${newLocale}${pathWithoutLocale}${searchString ? `?${searchString}` : ''}`;
    
    router.push(newPath);
  };

  if (variant === 'buttons') {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        {locales.map((lang) => (
          <Button
            key={lang}
            variant={locale === lang ? "default" : "ghost"}
            size="sm"
            onClick={() => switchLanguage(lang)}
            className="px-2 py-1 h-8 text-xs"
          >
            <span className="mr-1">{languageFlags[lang]}</span>
            {lang.toUpperCase()}
          </Button>
        ))}
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Globe className="w-4 h-4 text-muted-foreground" />
      <Select value={locale} onValueChange={(value) => switchLanguage(value as Locale)}>
        <SelectTrigger className="w-[120px] h-8 text-xs">
          <SelectValue>
            <span className="flex items-center">
              <span className="mr-2">{languageFlags[locale]}</span>
              {languageNames[locale]}
            </span>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {locales.map((lang) => (
            <SelectItem key={lang} value={lang}>
              <span className="flex items-center">
                <span className="mr-2">{languageFlags[lang]}</span>
                {languageNames[lang]}
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
