'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeMode, applyTheme, getInitialMode, setStoredMode } from '@/lib/themes';

interface ThemeContextType {
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  mounted: boolean;
  systemPreference: ThemeMode;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultMode?: ThemeMode;
}

export function ThemeProvider({ children, defaultMode = 'light' }: ThemeProviderProps) {
  const [mode, setModeState] = useState<ThemeMode>(defaultMode);
  const [mounted, setMounted] = useState(false);
  const [systemPreference, setSystemPreference] = useState<ThemeMode>('light');

  // Initialize theme on mount
  useEffect(() => {
    setMounted(true);

    // Get system preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const systemPref = mediaQuery.matches ? 'dark' : 'light';
    setSystemPreference(systemPref);

    // Get initial mode (URL > localStorage > system > default)
    const initialMode = getInitialMode();
    setModeState(initialMode);

    // Listen for system preference changes
    const handleSystemChange = (e: MediaQueryListEvent) => {
      const newSystemPref = e.matches ? 'dark' : 'light';
      setSystemPreference(newSystemPref);
    };

    mediaQuery.addEventListener('change', handleSystemChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemChange);
    };
  }, []);

  // Apply theme when mode changes
  useEffect(() => {
    if (mounted) {
      applyTheme(mode);
    }
  }, [mode, mounted]);

  const setMode = (newMode: ThemeMode) => {
    setModeState(newMode);
    setStoredMode(newMode);

    // Update URL parameter
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (newMode === 'light') {
        url.searchParams.delete('mode');
      } else {
        url.searchParams.set('mode', newMode);
      }
      window.history.replaceState({}, '', url.toString());
    }
  };

  const toggleMode = () => {
    const newMode = mode === 'light' ? 'dark' : 'light';
    setMode(newMode);
  };

  return (
    <ThemeContext.Provider value={{ mode, setMode, toggleMode, mounted, systemPreference }}>
      {children}
    </ThemeContext.Provider>
  );
}
