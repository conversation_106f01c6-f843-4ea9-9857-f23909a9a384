import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Plattform testen - AITELMED medPower® Telemedizin Konfigurator',
  description: 'Konfigurieren Sie Ihre ideale Telemedizin-Lösung mit unserem interaktiven Tool. Erhalten Sie sofortige Empfehlungen basierend auf den spezifischen Anforderungen Ihrer Praxis.',
};

export default function ConfiguratorPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="heading-xl mb-6">Plattform testen</h1>
          <p className="text-lg text-muted-foreground mb-8">
            Gestalten Sie Ihre perfekte Telemedizin-Lösung mit unserem interaktiven Konfigurator.
            Erhalten Sie sofortige Empfehlungen und Preise basierend auf den einzigartigen Anforderungen Ihrer Praxis.
          </p>
          <div className="bg-card border border-border rounded-lg p-8">
            <p className="text-muted-foreground">
              Der interaktive Plattform-Konfigurator wird in der nächsten Phase implementiert.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
