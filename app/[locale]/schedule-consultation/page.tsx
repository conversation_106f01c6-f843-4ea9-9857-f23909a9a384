import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Beratung vereinbaren - AITELMED Telemedizin Experten',
  description: 'Vereinbaren Sie eine persönliche Beratung mit unseren Telemedizin-Experten. Erhalten Sie maßgeschneiderte Beratung für die Implementierung von medPower® in Ihrer Praxis.',
};

export default function ScheduleConsultationPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="heading-xl mb-6">Beratung vereinbaren</h1>
          <p className="text-lg text-muted-foreground mb-8">
            Erhalten Sie persönliche Beratung von unseren Telemedizin-Experten.
            Wir helfen Ihnen zu verstehen, wie AITELMED's medPower® Plattform auf Ihre spezifischen Bedürfnisse zugeschnitten werden kann.
          </p>
          <div className="bg-card border border-border rounded-lg p-8">
            <p className="text-muted-foreground">
              Das Beratungsformular wird in der nächsten Phase implementiert.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
