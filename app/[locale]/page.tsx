import { Footer } from '@/components/sections/footer';
import { Head<PERSON> } from '@/components/sections/header';
import { HeroSection } from '@/components/sections/hero';
import { SolutionsSection } from '@/components/sections/solutions';
import { TrustSocialProofSection } from '@/components/sections/trust-social-proof';
import { getTranslations, setRequestLocale } from 'next-intl/server';

export default async function Home({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  // const t = await getTranslations('common');

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <HeroSection />
        <SolutionsSection />
        <TrustSocialProofSection />
      </main>
      <Footer />
    </div>
  );
}
