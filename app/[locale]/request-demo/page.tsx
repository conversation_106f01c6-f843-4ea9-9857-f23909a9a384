import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AITELMED - Telemedizin Plattform',
  description: 'Fordern Sie eine kostenlose Demonstration unserer GBA-konformen medPower® Telemedizin-Plattform an. <PERSON><PERSON><PERSON>, wie AITELMED Ihre Patientenversorgung digitalisiert.',
};

export default function RequestDemoPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="heading-xl mb-6">Kostenlose Demo anfordern</h1>
          <p className="text-lg text-muted-foreground mb-8">
            Erleben Sie die GBA-konforme medPower® Telemedizin-Plattform hautnah.
            Unsere Experten zeigen Ihnen, wie AITELMED Ihre Patientenversorgung digitalisiert und verbessert.
          </p>
          <div className="bg-card border border-border rounded-lg p-8">
            <p className="text-muted-foreground">
              Das Demo-Anfrageformular wird in der nächsten Phase implementiert.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
